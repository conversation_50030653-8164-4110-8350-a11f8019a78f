import { supabase } from '@/lib/supabase'
import type { 
  DesignAnalysis, 
  CreateDesignAnalysisData, 
  UpdateDesignAnalysisData,
  DesignUpload,
  CreateDesignUploadData
} from '@/lib/supabase'

/**
 * Service for managing design analysis data in Supabase
 */
export class DesignAnalysisService {

  /**
   * Upload an image file to Supabase Storage with comprehensive error handling
   */
  async uploadImage(file: File, userId: string): Promise<string> {
    try {
      // Validate inputs
      if (!file) {
        throw new Error('No file provided for upload')
      }

      if (!userId) {
        throw new Error('User ID is required for upload')
      }

      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        throw new Error(`File size (${(file.size / 1024 / 1024).toFixed(1)}MB) exceeds the 10MB limit`)
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      if (!allowedTypes.includes(file.type)) {
        throw new Error(`File type ${file.type} is not supported. Please use JPEG, PNG, GIF, WebP, or SVG`)
      }

      // Generate unique filename with user ID and timestamp
      const timestamp = Date.now()
      const fileExtension = file.name.split('.').pop() || 'jpg'
      const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
      const fileName = `${userId}/${timestamp}_${sanitizedName}`

      console.log('📤 Uploading image to Supabase Storage:', {
        fileName,
        fileSize: file.size,
        fileType: file.type
      })

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from('design-analysis-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        console.error('❌ Supabase Storage upload error:', error)

        // Handle specific error types
        if (error.message.includes('Duplicate')) {
          throw new Error('A file with this name already exists. Please try again.')
        } else if (error.message.includes('Policy')) {
          throw new Error('You do not have permission to upload files. Please check your authentication.')
        } else if (error.message.includes('size')) {
          throw new Error('File size exceeds the allowed limit. Please use a smaller image.')
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          throw new Error('Network error during upload. Please check your connection and try again.')
        } else {
          throw new Error(`Upload failed: ${error.message}`)
        }
      }

      if (!data?.path) {
        throw new Error('Upload completed but no file path was returned')
      }

      // Get public URL for the uploaded file
      const { data: { publicUrl } } = supabase.storage
        .from('design-analysis-images')
        .getPublicUrl(fileName)

      if (!publicUrl) {
        throw new Error('Failed to generate public URL for uploaded image')
      }

      console.log('✅ Image uploaded successfully:', {
        path: data.path,
        publicUrl
      })

      return publicUrl
    } catch (error) {
      console.error('💥 Error in uploadImage:', error)

      // Re-throw with more context if it's a generic error
      if (error instanceof Error) {
        throw error
      } else {
        throw new Error('An unexpected error occurred during image upload')
      }
    }
  }

  /**
   * Save a new design analysis to the database with optional image upload and comprehensive error handling
   */
  async saveAnalysis(analysisData: CreateDesignAnalysisData, imageFile?: File): Promise<DesignAnalysis> {
    let uploadedImageUrl: string | null = null

    try {
      let finalAnalysisData = { ...analysisData }

      // Upload image if provided
      if (imageFile && analysisData.user_id) {
        console.log('💾 Starting image upload for analysis save...')

        try {
          uploadedImageUrl = await this.uploadImage(imageFile, analysisData.user_id)
          finalAnalysisData.file_url = uploadedImageUrl
          console.log('✅ Image uploaded successfully, proceeding with analysis save')
        } catch (uploadError) {
          console.error('❌ Image upload failed:', uploadError)

          // Check if it's a critical error or if we can continue without image
          const errorMessage = uploadError instanceof Error ? uploadError.message : 'Unknown error'

          if (errorMessage.includes('permission') || errorMessage.includes('authentication')) {
            // Critical auth errors - don't save analysis
            throw new Error(`Authentication error: ${errorMessage}`)
          } else if (errorMessage.includes('size') || errorMessage.includes('type')) {
            // File validation errors - don't save analysis
            throw new Error(`File validation error: ${errorMessage}`)
          } else {
            // Network or other non-critical errors - save analysis without image
            console.warn('⚠️ Continuing to save analysis without image due to upload failure')
            finalAnalysisData.file_url = null

            // Don't throw error, but let the caller know about the upload issue
            console.log('📝 Analysis will be saved without image due to upload failure')
          }
        }
      }

      console.log('💾 Saving analysis to database...')

      const { data, error } = await supabase
        .schema('api')
        .from('design_analyses')
        .insert(finalAnalysisData)
        .select()
        .single()

      if (error) {
        console.error('❌ Database save error:', error)

        // If we uploaded an image but database save failed, we should clean up the image
        if (uploadedImageUrl && imageFile) {
          console.log('🧹 Cleaning up uploaded image due to database save failure...')
          try {
            // Extract file path from URL for cleanup
            const url = new URL(uploadedImageUrl)
            const filePath = url.pathname.split('/').slice(-2).join('/')
            await supabase.storage
              .from('design-analysis-images')
              .remove([filePath])
            console.log('✅ Cleanup completed')
          } catch (cleanupError) {
            console.error('❌ Failed to cleanup uploaded image:', cleanupError)
          }
        }

        // Handle specific database errors
        if (error.message.includes('duplicate') || error.code === '23505') {
          throw new Error('An analysis with similar data already exists')
        } else if (error.message.includes('permission') || error.code === '42501') {
          throw new Error('You do not have permission to save analyses. Please check your authentication.')
        } else if (error.message.includes('network') || error.message.includes('connection')) {
          throw new Error('Network error while saving. Please check your connection and try again.')
        } else {
          throw new Error(`Failed to save analysis: ${error.message}`)
        }
      }

      if (!data) {
        throw new Error('Analysis was saved but no data was returned')
      }

      console.log('✅ Analysis saved successfully:', data.id)
      return data
    } catch (error) {
      console.error('💥 Error in saveAnalysis:', error)

      // Re-throw with more context if it's a generic error
      if (error instanceof Error) {
        throw error
      } else {
        throw new Error('An unexpected error occurred while saving the analysis')
      }
    }
  }

  /**
   * Save a new design analysis to the database (legacy method without image upload)
   */
  async saveAnalysisLegacy(analysisData: CreateDesignAnalysisData): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .insert(analysisData)
      .select()
      .single()

    if (error) {
      console.error('Error saving design analysis:', error)
      throw new Error(`Failed to save analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Retrieve image from Supabase Storage with comprehensive error handling
   */
  async getImageUrl(filePath: string): Promise<string | null> {
    try {
      if (!filePath) {
        console.warn('⚠️ No file path provided to getImageUrl')
        return null
      }

      // If it's already a full URL, validate and return it
      if (filePath.startsWith('http')) {
        console.log('🔗 File path is already a full URL:', filePath)
        return filePath
      }

      console.log('📥 Getting public URL for file path:', filePath)

      // Get public URL for the file
      const { data: { publicUrl } } = supabase.storage
        .from('design-analysis-images')
        .getPublicUrl(filePath)

      if (!publicUrl) {
        console.error('❌ Failed to generate public URL for file:', filePath)
        return null
      }

      console.log('✅ Generated public URL:', publicUrl)
      return publicUrl
    } catch (error) {
      console.error('💥 Error getting image URL:', error)
      return null
    }
  }

  /**
   * Check if an image exists in Supabase Storage
   */
  async checkImageExists(filePath: string): Promise<boolean> {
    try {
      if (!filePath) return false

      // Extract the file path from URL if needed
      let actualPath = filePath
      if (filePath.startsWith('http')) {
        const url = new URL(filePath)
        actualPath = url.pathname.split('/').slice(-2).join('/') // Get user_id/filename
      }

      const { data, error } = await supabase.storage
        .from('design-analysis-images')
        .list(actualPath.split('/')[0], {
          search: actualPath.split('/')[1]
        })

      if (error) {
        console.error('Error checking if image exists:', error)
        return false
      }

      return data && data.length > 0
    } catch (error) {
      console.error('Error in checkImageExists:', error)
      return false
    }
  }

  /**
   * Test Supabase Storage connection and permissions
   */
  async testStorageConnection(userId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Test bucket access by listing files
      const { data, error } = await supabase.storage
        .from('design-analysis-images')
        .list(userId, { limit: 1 })

      if (error) {
        if (error.message.includes('permission') || error.message.includes('policy')) {
          return {
            success: false,
            message: 'No tienes permisos para acceder al almacenamiento. Verifica tu autenticación.'
          }
        } else if (error.message.includes('bucket')) {
          return {
            success: false,
            message: 'El bucket de almacenamiento no está disponible.'
          }
        } else {
          return {
            success: false,
            message: `Error de conexión: ${error.message}`
          }
        }
      }

      return {
        success: true,
        message: 'Conexión al almacenamiento exitosa'
      }
    } catch (error) {
      return {
        success: false,
        message: `Error inesperado: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Get all design analyses for the current user
   */
  async getUserAnalyses(userId: string, options?: {
    limit?: number
    offset?: number
    toolType?: string
    isFavorite?: boolean
    orderBy?: 'created_at' | 'updated_at' | 'overall_score'
    orderDirection?: 'asc' | 'desc'
  }): Promise<DesignAnalysis[]> {
    let query = supabase
      .schema('api')
      .from('design_analyses')
      .select('*')
      .eq('user_id', userId)

    // Apply filters
    if (options?.toolType) {
      query = query.eq('tool_type', options.toolType)
    }
    
    if (options?.isFavorite !== undefined) {
      query = query.eq('is_favorite', options.isFavorite)
    }

    // Apply ordering
    const orderBy = options?.orderBy || 'created_at'
    const orderDirection = options?.orderDirection || 'desc'
    query = query.order(orderBy, { ascending: orderDirection === 'asc' })

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit)
    }
    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching user analyses:', error)
      throw new Error(`Failed to fetch analyses: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get a specific design analysis by ID
   */
  async getAnalysisById(id: string): Promise<DesignAnalysis | null> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching analysis:', error)
      throw new Error(`Failed to fetch analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Update a design analysis
   */
  async updateAnalysis(updateData: UpdateDesignAnalysisData): Promise<DesignAnalysis> {
    const { id, ...updates } = updateData

    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating analysis:', error)
      throw new Error(`Failed to update analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a design analysis
   */
  async deleteAnalysis(id: string): Promise<void> {
    const { error } = await supabase
      .schema('api')
      .from('design_analyses')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting analysis:', error)
      throw new Error(`Failed to delete analysis: ${error.message}`)
    }
  }

  /**
   * Get recent design analyses (last 10)
   */
  async getRecentAnalyses(): Promise<DesignAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    return this.getUserAnalyses(user.id, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Get favorite design analyses
   */
  async getFavoriteAnalyses(): Promise<DesignAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    return this.getUserAnalyses(user.id, {
      isFavorite: true,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Toggle favorite status of an analysis
   */
  async toggleFavorite(id: string): Promise<DesignAnalysis> {
    // First get the current analysis to toggle its favorite status
    const currentAnalysis = await this.getAnalysisById(id)
    if (!currentAnalysis) {
      throw new Error('Analysis not found')
    }

    const newFavoriteStatus = !currentAnalysis.is_favorite

    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({ is_favorite: newFavoriteStatus })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error toggling favorite:', error)
      throw new Error(`Failed to toggle favorite: ${error.message}`)
    }

    return data
  }

  /**
   * Rename an analysis
   */
  async renameAnalysis(id: string, newName: string): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({ custom_name: newName.trim() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error renaming analysis:', error)
      throw new Error(`Failed to rename analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Record view for an analysis
   */
  async recordView(id: string): Promise<void> {
    const currentAnalysis = await this.getAnalysisById(id)
    if (!currentAnalysis) {
      return
    }

    // Try to update with last_viewed_at first, fallback to just view_count if column doesn't exist
    let updateData: any = {
      view_count: (currentAnalysis.view_count || 0) + 1,
      last_viewed_at: new Date().toISOString()
    }

    let { error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update(updateData)
      .eq('id', id)

    // If error mentions last_viewed_at column, try without it
    if (error && error.message.includes('last_viewed_at')) {
      console.warn('last_viewed_at column not found, updating only view_count')
      updateData = {
        view_count: (currentAnalysis.view_count || 0) + 1
      }

      const { error: fallbackError } = await supabase
        .schema('api')
        .from('design_analyses')
        .update(updateData)
        .eq('id', id)

      if (fallbackError) {
        console.error('Error recording view (fallback):', fallbackError)
      }
    } else if (error) {
      console.error('Error recording view:', error)
    }
  }

  /**
   * Add tags to an analysis
   */
  async updateTags(id: string, tags: string[]): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({ tags })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating tags:', error)
      throw new Error(`Failed to update tags: ${error.message}`)
    }

    return data
  }

  /**
   * Add notes to an analysis
   */
  async updateNotes(id: string, notes: string): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({ notes })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating notes:', error)
      throw new Error(`Failed to update notes: ${error.message}`)
    }

    return data
  }

  /**
   * Save uploaded file information
   */
  async saveUpload(uploadData: CreateDesignUploadData): Promise<DesignUpload> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_uploads')
      .insert(uploadData)
      .select()
      .single()

    if (error) {
      console.error('Error saving upload:', error)
      throw new Error(`Failed to save upload: ${error.message}`)
    }

    return data
  }

  /**
   * Get user's upload history
   */
  async getUserUploads(userId: string, limit = 50): Promise<DesignUpload[]> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_uploads')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching uploads:', error)
      throw new Error(`Failed to fetch uploads: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get analysis statistics for a user
   */
  async getUserStats(userId: string): Promise<{
    totalAnalyses: number
    favoriteAnalyses: number
    averageScore: number
    toolTypeBreakdown: Record<string, number>
    recentActivity: number // analyses in last 7 days
  }> {
    // Get total count and favorites
    const { data: analyses, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .select('overall_score, tool_type, is_favorite, created_at')
      .eq('user_id', userId)

    if (error) {
      console.error('Error fetching user stats:', error)
      throw new Error(`Failed to fetch user stats: ${error.message}`)
    }

    if (!analyses || analyses.length === 0) {
      return {
        totalAnalyses: 0,
        favoriteAnalyses: 0,
        averageScore: 0,
        toolTypeBreakdown: {},
        recentActivity: 0
      }
    }

    const totalAnalyses = analyses.length
    const favoriteAnalyses = analyses.filter(a => a.is_favorite).length
    const averageScore = analyses.reduce((sum, a) => sum + a.overall_score, 0) / totalAnalyses
    
    // Tool type breakdown
    const toolTypeBreakdown: Record<string, number> = {}
    analyses.forEach(a => {
      toolTypeBreakdown[a.tool_type] = (toolTypeBreakdown[a.tool_type] || 0) + 1
    })

    // Recent activity (last 7 days)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const recentActivity = analyses.filter(a => 
      new Date(a.created_at) > sevenDaysAgo
    ).length

    return {
      totalAnalyses,
      favoriteAnalyses,
      averageScore: Math.round(averageScore * 100) / 100,
      toolTypeBreakdown,
      recentActivity
    }
  }


}

// Export singleton instance
export const designAnalysisService = new DesignAnalysisService()
